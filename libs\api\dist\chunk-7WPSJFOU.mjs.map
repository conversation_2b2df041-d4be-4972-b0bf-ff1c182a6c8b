{"version": 3, "sources": ["../src/sitemap.ts", "../src/common.ts", "../src/consts.ts", "../src/auth.ts", "../src/user.ts", "../src/commune.ts", "../src/reactor.ts", "../src/tag.ts", "../src/rating.ts"], "sourcesContent": ["import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { id } from \"./common\";\n\nexport type GetSitemapGenerationDataOutput = Infer<typeof GetSitemapGenerationDataOutputSchema>;\nexport const GetSitemapGenerationDataOutputSchema = z.object({\n    communeIds: z.array(id),\n    reactorPostIds: z.array(id),\n    reactorHubIds: z.array(id),\n    reactorCommunityIds: z.array(id),\n});\n", "import { PAGE_SIZE } from \"./consts\";\nimport type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\n\nexport const id = z.string().nanoid();\nexport const idOrNull = id.nullable().default(null);\n\nexport const url = z.string().url();\nexport const email = z.string().email();\nexport const query = z.string().nonempty();\n\nexport const imageUrl = z.string().nonempty();\nexport const maybeImageUrl = imageUrl.nullable();\n\nexport const createdAt = z.date();\nexport const updatedAt = z.date();\nexport const deletedAt = z.date().nullable();\n\nexport const searchIds = z.array(id).min(1);\nexport const searchQuery = z.string().nonempty();\n\nexport const stringToDate = z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date());\n\nexport function JsonStringToObject<T extends z.ZodRawShape>(schema: T) {\n    return z\n        .string()\n        .transform((value) => JSON.parse(value))\n        .pipe(z.object(schema));\n}\n\nexport function FormDataToObject<T extends z.ZodRawShape>(schema: T) {\n    return z.object({\n        data: JsonStringToObject(schema),\n    });\n}\n\nexport type ObjectWithId = Infer<typeof ObjectWithIdSchema>;\nexport const ObjectWithIdSchema = z.object({ id });\n\nexport type WebsiteLocale = Infer<typeof WebsiteLocaleSchema>;\nexport const WebsiteLocaleSchema = z.enum([\"en\", \"ru\"]);\n\nexport type LocalizationLocale = Infer<typeof LocalizationLocaleSchema>;\nexport const LocalizationLocaleSchema = z.enum([\"en\", \"ru\"]);\n\nexport type LocalizationLocales = Infer<typeof LocalizationLocalesSchema>;\nexport const LocalizationLocalesSchema = z.array(LocalizationLocaleSchema).min(1);\n\nexport type Localization = Infer<typeof LocalizationSchema>;\nexport const LocalizationSchema = z.object({\n    locale: LocalizationLocaleSchema,\n    value: z.string().nonempty(),\n});\n\nexport type Localizations = Infer<typeof LocalizationsSchema>;\nexport const LocalizationsSchema = z.array(LocalizationSchema);\n\nexport type Image = Infer<typeof ImageSchema>;\nexport const ImageSchema = z.object({\n    id,\n    url: z.string(),\n    createdAt: stringToDate,\n    updatedAt: stringToDate,\n});\n\nexport type Images = Infer<typeof ImagesSchema>;\nexport const ImagesSchema = z.array(ImageSchema);\n\nexport const pagination = {\n    offset: z.coerce.number()\n        .int()\n        .default(0),\n    limit: z.coerce.number()\n        .int()\n        .positive()\n        .max(100)\n        .default(PAGE_SIZE),\n\n    page: z.coerce.number()\n        .int()\n        .positive()\n        .default(1),\n    size: z.coerce.number()\n        .int()\n        .positive()\n        .max(100)\n        .default(PAGE_SIZE),\n};\n\nexport type Pagination = Infer<typeof PaginationSchema>;\nexport const PaginationSchema = z\n    .object({\n        page: pagination.page,\n        size: pagination.size,\n    })\n    .default({\n        page: 1,\n        size: PAGE_SIZE,\n    });\n\nexport function parseInput<T extends z.ZodTypeAny>(\n    schema: T,\n    value: z.input<T>,\n): z.output<T> {\n    return schema.parse(value);\n}\n\nexport function parseUnknown<T extends z.ZodTypeAny>(\n    schema: T,\n    value: unknown,\n): z.output<T> {\n    return schema.parse(value);\n}\n", "export const PAGE_SIZE = 20;\n\nexport const ALLOWED_IMAGE_FILE_TYPES = [\"image/jpeg\", \"image/png\", \"image/webp\"];\nexport const MAX_IMAGE_FILE_SIZE = 5 * 1024 * 1024; // 5MB\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { email, id, imageUrl } from \"./common\";\nimport { userDescription, userName, UserRoleSchema } from \"./user\";\n\nexport const otp = z.string().nonempty().length(6);\n\nexport type SendOtpInput = Infer<typeof SendOtpInputSchema>;\nexport const SendOtpInputSchema = z.object({\n    email,\n});\n\nexport type SendOtpOutput = Infer<typeof SendOtpOutputSchema>;\nexport const SendOtpOutputSchema = z.object({\n    isSent: z.boolean(),\n});\n\nexport type SignupInput = Infer<typeof SignupInputSchema>;\nexport const SignupInputSchema = z.object({\n    referrerId: id.nullable(),\n    email,\n    otp,\n});\n\nexport type SigninInput = Infer<typeof SigninInputSchema>;\nexport const SigninInputSchema = z.object({\n    email,\n    otp,\n});\n\nexport type SuccessfulOutput = Infer<typeof SuccessfulOutputSchema>;\nexport const SuccessfulOutputSchema = z.object({\n    id,\n    email,\n    role: UserRoleSchema,\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport {\n    id,\n    email,\n    imageUrl,\n    searchIds,\n    searchQuery,\n    createdAt,\n    updatedAt,\n    deletedAt,\n    LocalizationsSchema,\n    PaginationSchema,\n    LocalizationLocaleSchema,\n} from \"./common\";\n\nexport const userName = LocalizationsSchema.min(1);\nexport const userDescription = LocalizationsSchema;\nexport const userImage = imageUrl.nullable();\n\nexport const userTitleName = LocalizationsSchema.min(1);\nexport const userTitleIsActive = z.boolean();\nexport const userTitleColor = z.string().nonempty().nullable();\n\nexport const userNoteText = z.string().nonempty();\n\nexport type UserRole = Infer<typeof UserRoleSchema>;\nexport const UserRoleSchema = z.enum([\n    \"admin\",\n    \"moderator\",\n    \"user\",\n]);\n\nexport type UserAlignmentSystemType = Infer<typeof UserAlignmentSystemTypeSchema>;\nexport const UserAlignmentSystemTypeSchema = z.enum([\n    \"lawfulGood\",\n    \"neutralGood\",\n    \"chaoticGood\",\n    \"lawfulNeutral\",\n    \"trueNeutral\",\n    \"chaoticNeutral\",\n    \"lawfulEvil\",\n    \"neutralEvil\",\n    \"chaoticEvil\",\n]);\n\nexport const userAlignmentSystemType = UserAlignmentSystemTypeSchema.nullable();\n\nexport type SimpleUser = Infer<typeof SimpleUserSchema>;\nexport const SimpleUserSchema = z.object({\n    id,\n    name: userName,\n    image: userImage,\n});\n\nexport type GetMeOutput = Infer<typeof GetMeOutputSchema>;\nexport const GetMeOutputSchema = z.object({\n    id,\n    email,\n    role: UserRoleSchema,\n\n    name: userName,\n    description: userDescription,\n\n    image: imageUrl.nullable(),\n\n    alignmentSystemType: userAlignmentSystemType,\n\n    createdAt,\n    updatedAt,\n});\n\nexport type GetUsersInput = Infer<typeof GetUsersInputSchema>;\nexport const GetUsersInputSchema = z\n    .object({\n        pagination: PaginationSchema,\n\n        ids: searchIds,\n        query: searchQuery,\n    })\n    .partial();\n\nexport type GetUserOutput = Infer<typeof GetUserOutputSchema>;\nexport const GetUserOutputSchema = z.object({\n    id,\n    role: UserRoleSchema,\n\n    name: userName,\n    description: userDescription,\n\n    image: userImage,\n\n    alignmentSystemType: userAlignmentSystemType,\n\n    createdAt,\n    updatedAt,\n    deletedAt: deletedAt.optional(),\n});\n\nexport type GetUsersOutput = Infer<typeof GetUsersOutputSchema>;\nexport const GetUsersOutputSchema = z.array(GetUserOutputSchema);\n\nexport type UpdateUserInput = Infer<typeof UpdateUserInputSchema>;\nexport const UpdateUserInputSchema = z\n    .object({\n        id,\n\n        name: userName.optional(),\n        description: userDescription.optional(),\n\n        alignmentSystemType: userAlignmentSystemType.optional(),\n    });\n\n\nexport type CreateUserTitleInput = Infer<typeof CreateUserTitleInputSchema>;\nexport const CreateUserTitleInputSchema = z.object({\n    userId: id,\n\n    name: userTitleName,\n\n    isActive: userTitleIsActive,\n    color: userTitleColor,\n});\n\nexport type UpdateUserTitleInput = Infer<typeof UpdateUserTitleInputSchema>;\nexport const UpdateUserTitleInputSchema = z\n    .object({\n        id,\n\n        name: userTitleName.optional(),\n\n        isActive: userTitleIsActive.optional(),\n        color: userTitleColor.optional(),\n    });\n\nexport type GetUserTitlesInput = Infer<typeof GetUserTitlesInputSchema>;\nexport const GetUserTitlesInputSchema = z.object({\n    userId: id,\n    ids: searchIds.optional(),\n    isActive: userTitleIsActive.optional(),\n});\n\nexport type GetUserTitlesOutput = Infer<typeof GetUserTitlesOutputSchema>;\nexport const GetUserTitlesOutputSchema = z.array(\n    z.object({\n        id,\n    \n        userId: id,\n\n        name: userTitleName,\n    \n        isActive: userTitleIsActive,\n        color: userTitleColor,\n    \n        createdAt,\n        updatedAt,\n        deletedAt: deletedAt.optional(),\n    }),\n);\n\nexport type GetUserNoteInput = Infer<typeof GetUserNoteInputSchema>;\nexport const GetUserNoteInputSchema = z.object({\n    userId: id,\n});\n\nexport type GetUserNoteOutput = Infer<typeof GetUserNoteOutputSchema>;\nexport const GetUserNoteOutputSchema = z.object({\n    text: userNoteText.nullable(),\n});\n\nexport type UpdateUserNoteInput = Infer<typeof UpdateUserNoteInputSchema>;\nexport const UpdateUserNoteInputSchema = z.object({\n    userId: id,\n\n    text: userNoteText.nullable(),\n});\n\nexport type GetUserInvitesInput = Infer<typeof GetUserInvitesInputSchema>;\nexport const GetUserInvitesInputSchema = z.object({\n    pagination: PaginationSchema,\n});\n\nexport type GetUserInvitesOutput = Infer<typeof GetUserInvitesOutputSchema>;\nexport const GetUserInvitesOutputSchema = z.array(z.object({\n    id,\n    email,\n    name: z.string().nonempty().nullable(),\n    locale: LocalizationLocaleSchema,\n    isUsed: z.boolean(),\n}));\n\nexport type UpsertUserInviteInput = Infer<typeof UpsertUserInviteInputSchema>;\nexport const UpsertUserInviteInputSchema = z.object({\n    email,\n    name: z.string().nonempty().nullable(),\n    locale: LocalizationLocaleSchema,\n});\n\nexport type DeleteUserInviteInput = Infer<typeof DeleteUserInviteInputSchema>;\nexport const DeleteUserInviteInputSchema = z.object({\n    id,\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport {\n    id,\n    searchIds,\n    searchQuery,\n    maybeImageUrl,\n    createdAt,\n    updatedAt,\n    deletedAt,\n    JsonStringToObject,\n    LocalizationsSchema,\n    PaginationSchema,\n} from \"./common\";\nimport { userName } from \"./user\";\n\n// communes\n\nexport type CommuneMemberType = Infer<typeof CommuneMemberTypeSchema>;\nexport const CommuneMemberTypeSchema = z.enum([\"user\"]);\n\nexport const communeName = LocalizationsSchema.min(1);\nexport const communeDescription = LocalizationsSchema;\n\nexport const communeMemberActorType = CommuneMemberTypeSchema;\nexport const communeMemberName = z.union([userName, communeName]);\n\nexport type TransferHeadStatusInput = Infer<typeof TransferHeadStatusInputSchema>;\nexport const TransferHeadStatusInputSchema = z.object({\n    communeId: id,\n    newHeadUserId: id,\n});\n\nexport type GetCommunesInput = Infer<typeof GetCommunesInputSchema>;\nexport const GetCommunesInputSchema = z\n    .object({\n        pagination: PaginationSchema,\n\n        ids: searchIds,\n        query: searchQuery,\n        userId: id,\n    })\n    .partial();\n\nexport type GetCommuneOutput = Infer<typeof GetCommuneOutputSchema>;\nexport const GetCommuneOutputSchema = z.object({\n    id,\n\n    name: communeName,\n    description: communeDescription,\n\n    headMember: z.object({\n        actorType: communeMemberActorType,\n        actorId: id,\n\n        name: communeMemberName,\n\n        image: maybeImageUrl,\n    }),\n\n    memberCount: z.number().int().positive(),\n\n    image: maybeImageUrl,\n\n    createdAt,\n    updatedAt,\n    deletedAt: deletedAt.optional(),\n})\n\nexport type GetCommunesOutput = Infer<typeof GetCommunesOutputSchema>;\nexport const GetCommunesOutputSchema = z.array(GetCommuneOutputSchema);\n\nexport type CreateCommuneInput = Infer<typeof CreateCommuneInputSchema>;\nexport const CreateCommuneInputSchema = z.object({\n    headUserId: id.optional(),\n\n    name: communeName,\n    description: communeDescription,\n});\n\nexport type UpdateCommuneInput = Infer<typeof UpdateCommuneInputSchema>;\nexport const UpdateCommuneInputSchema = z\n    .object({\n        id,\n\n        name: communeName.optional(),\n        description: communeDescription.optional(),\n    });\n\n// commune members\n\nexport type GetCommuneMembersInput = Infer<typeof GetCommuneMembersInputSchema>;\nexport const GetCommuneMembersInputSchema = z.object({\n    pagination: PaginationSchema,\n\n    communeId: id,\n});\n\nexport type GetCommuneMemberOutput = Infer<typeof GetCommuneMemberOutputSchema>;\nexport const GetCommuneMemberOutputSchema = z.object({\n    id,\n\n    actorType: communeMemberActorType,\n    actorId: id,\n\n    name: communeMemberName,\n\n    image: maybeImageUrl,\n\n    createdAt,\n    deletedAt: deletedAt.nullable(),\n});\n\nexport type GetCommuneMembersOutput = Infer<typeof GetCommuneMembersOutputSchema>;\nexport const GetCommuneMembersOutputSchema = z.array(GetCommuneMemberOutputSchema);\n\nexport type CreateCommuneMemberInput = Infer<typeof CreateCommuneMemberInputSchema>;\nexport const CreateCommuneMemberInputSchema = z.object({\n    communeId: id,\n    userId: id,\n});\n\n// commune invitations\n\nexport type CommuneInvitationStatus = Infer<typeof CommuneInvitationStatusSchema>;\nexport const CommuneInvitationStatusSchema = z.enum([\"pending\", \"accepted\", \"rejected\", \"expired\"]);\n\nexport type GetCommuneInvitationsInput = Infer<typeof GetCommuneInvitationsInputSchema>;\nexport const GetCommuneInvitationsInputSchema = z.object({\n    pagination: PaginationSchema,\n\n    communeId: id.optional(),\n});\n\nexport type GetCommuneInvitationsOutput = Infer<typeof GetCommuneInvitationsOutputSchema>;\nexport const GetCommuneInvitationsOutputSchema = z.array(\n    z.object({\n        id,\n\n        communeId: id,\n        userId: id,\n\n        status: CommuneInvitationStatusSchema,\n\n        createdAt: z.date(),\n        updatedAt: z.date(),\n    }),\n);\n\nexport type CreateCommuneInvitationInput = Infer<typeof CreateCommuneInvitationInputSchema>;\nexport const CreateCommuneInvitationInputSchema = z.object({\n    communeId: id,\n    userId: id,\n});\n\n// commune join requests\n\nexport type CommuneJoinRequestStatus = Infer<typeof CommuneJoinRequestStatusSchema>;\nexport const CommuneJoinRequestStatusSchema = z.enum([\"pending\", \"accepted\", \"rejected\"]);\n\nexport type GetCommuneJoinRequestsInput = Infer<typeof GetCommuneJoinRequestsInputSchema>;\nexport const GetCommuneJoinRequestsInputSchema = z.object({\n    pagination: PaginationSchema,\n\n    communeId: id.optional(),\n});\n\nexport type GetCommuneJoinRequestsOutput = Infer<typeof GetCommuneJoinRequestsOutputSchema>;\nexport const GetCommuneJoinRequestsOutputSchema = z.array(\n    z.object({\n        id,\n\n        communeId: id,\n        userId: id,\n\n        status: CommuneJoinRequestStatusSchema,\n\n        createdAt: z.date(),\n        updatedAt: z.date(),\n    })\n);\n\nexport type CreateCommuneJoinRequestInput = Infer<typeof CreateCommuneJoinRequestInputSchema>;\nexport const CreateCommuneJoinRequestInputSchema = z.object({\n    communeId: id,\n    userId: id,\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport {\n    id,\n    searchIds,\n    searchQuery,\n    maybeImageUrl,\n    createdAt,\n    updatedAt,\n    deletedAt,\n    LocalizationsSchema,\n    PaginationSchema,\n    imageUrl,\n} from \"./common\";\nimport { SimpleUserSchema } from \"./user\";\nimport { tagName } from \"./tag\";\n\n// common\n\nexport type RatingType = Infer<typeof RatingTypeSchema>;\nexport const RatingTypeSchema = z.enum([\"like\", \"dislike\"]);\n\nexport type Rating = Infer<typeof RatingSchema>;\nexport const RatingSchema = z.object({\n    likes: z.number().int().nonnegative(),\n    dislikes: z.number().int().nonnegative(),\n    status: RatingTypeSchema.nullable(),\n});\n\nexport const hubName = LocalizationsSchema.min(1);\nexport const hubDescription = LocalizationsSchema.min(1);\nexport const hubImage = maybeImageUrl;\n\nexport const communityName = LocalizationsSchema.min(1);\nexport const communityDescription = LocalizationsSchema.min(1);\nexport const communityImage = maybeImageUrl;\n\n// posts\n\nexport const postUsefulness = z.number().int().min(0).max(10);\n\nexport type PostUsefulness = Infer<typeof PostUsefulnessSchema>;\nexport const PostUsefulnessSchema = z.object({\n    value: postUsefulness.nullable(),\n    count: z.number().int().nonnegative(),\n    totalValue: z.number().min(0).max(10).nullable(),\n});\n\nexport const postTitle = LocalizationsSchema.min(1);\nexport const postBody = LocalizationsSchema.min(1);\n\nexport type PostImage = Infer<typeof PostImageSchema>;\nexport const PostImageSchema = z.object({\n    id,\n    url: imageUrl,\n});\n\nexport type GetPostOutput = Infer<typeof GetPostsOutputSchema>;\nexport const GetPostOutputSchema = z.object({\n    id,\n\n    hub: z\n        .object({\n            id,\n            name: hubName,\n            image: hubImage,\n        })\n        .nullable(),\n\n    community: z\n        .object({\n            id,\n            name: communityName,\n            image: communityImage,\n        })\n        .nullable(),\n\n    author: SimpleUserSchema,\n\n    rating: RatingSchema,\n    usefulness: PostUsefulnessSchema,\n\n    title: postTitle,\n    body: postBody,\n\n    tags: z.array(\n        z.object({\n            id,\n            name: tagName,\n        }),\n    ),\n\n    createdAt,\n    updatedAt,\n    deletedAt: deletedAt.optional(),\n});\n\nexport type GetPostsInput = Infer<typeof GetPostsInputSchema>;\nexport const GetPostsInputSchema = z.object({\n    pagination: PaginationSchema,\n\n    id: id.optional(),\n    lensId: id.nullable(),\n});\n\nexport type GetPostsOutput = Infer<typeof GetPostsOutputSchema>;\nexport const GetPostsOutputSchema = z.array(GetPostOutputSchema);\n\nexport type GetPostImagesInput = Infer<typeof GetPostImagesInputSchema>;\nexport const GetPostImagesInputSchema = z.object({\n    id,\n});\n\nexport type GetPostImagesOutput = Infer<typeof GetPostImagesOutputSchema>;\nexport const GetPostImagesOutputSchema = z.array(PostImageSchema);\n\nexport type CreatePostInput = Infer<typeof CreatePostInputSchema>;\nexport const CreatePostInputSchema = z.object({\n    hubId: id.nullable(),\n    communityId: id.nullable(),\n    title: postTitle,\n    body: postBody,\n    tagIds: z.array(id),\n    imageIds: z.array(id),\n});\n\nexport type UpdatePostInput = Infer<typeof UpdatePostInputSchema>;\nexport const UpdatePostInputSchema = z\n    .object({\n        id,\n\n        title: postTitle.optional(),\n        body: postBody.optional(),\n        tagIds: z.array(id).optional(),\n        imageIds: z.array(id).optional(),\n    });\n\nexport type DeletePostInput = Infer<typeof DeletePostInputSchema>;\nexport const DeletePostInputSchema = z.object({\n    id,\n\n    reason: z.string().nonempty().nullable(),\n});\n\nexport type UpdatePostRatingInput = Infer<typeof UpdatePostRatingInputSchema>;\nexport const UpdatePostRatingInputSchema = z.object({\n    id,\n\n    type: RatingTypeSchema,\n});\n\nexport type UpdatePostRatingOutput = Infer<typeof UpdatePostRatingOutputSchema>;\nexport const UpdatePostRatingOutputSchema = RatingSchema;\n\nexport type UpdatePostUsefulnessInput = Infer<typeof UpdatePostUsefulnessInputSchema>;\nexport const UpdatePostUsefulnessInputSchema = z.object({\n    id,\n\n    value: postUsefulness.nullable(),\n});\n\nexport type UpdatePostUsefulnessOutput = Infer<typeof UpdatePostUsefulnessOutputSchema>;\nexport const UpdatePostUsefulnessOutputSchema = PostUsefulnessSchema;\n\n// comments\n\nexport type CommentEntityType = Infer<typeof CommentEntityTypeSchema>;\nexport const CommentEntityTypeSchema = z.enum([\"post\", \"comment\"]);\n\nexport const commentBody = LocalizationsSchema.min(1);\n\nexport type GetCommentsInput = Infer<typeof GetCommentsInputSchema>;\nexport const GetCommentsInputSchema = z.union([\n    z.object({\n        id,\n        entityType: z.never().optional(),\n        entityId: z.never().optional(),\n    }),\n    z.object({\n        id: z.never().optional(),\n        entityType: CommentEntityTypeSchema,\n        entityId: id,\n    }),\n]);\n\nexport type GetCommentsOutput = Infer<typeof GetCommentsOutputSchema>;\nexport const GetCommentsOutputSchema = z.array(\n    z.object({\n        id,\n\n        path: z.string().nonempty(),\n\n        author: SimpleUserSchema.nullable(),\n\n        isAnonymous: z.boolean(),\n        anonimityReason: z.string().nonempty().nullable(),\n\n        rating: RatingSchema,\n\n        body: commentBody.nullable(),\n\n        childrenCount: z.number().int().nonnegative(),\n\n        deleteReason: z.string().nonempty().nullable(),\n\n        createdAt,\n        updatedAt,\n        deletedAt,\n    })\n);\n\nexport type CreateCommentInput = Infer<typeof CreateCommentInputSchema>;\nexport const CreateCommentInputSchema = z.object({\n    entityType: CommentEntityTypeSchema,\n    entityId: id,\n\n    body: commentBody,\n});\n\nexport type UpdateCommentInput = Infer<typeof UpdateCommentInputSchema>;\nexport const UpdateCommentInputSchema = z\n    .object({\n        id,\n\n        body: commentBody.optional(),\n    });\n\nexport type DeleteCommentInput = Infer<typeof DeleteCommentInputSchema>;\nexport const DeleteCommentInputSchema = z.object({\n    id,\n\n    reason: z.string().nonempty().nullable(),\n});\n\nexport type UpdateCommentRatingInput = Infer<typeof UpdateCommentRatingInputSchema>;\nexport const UpdateCommentRatingInputSchema = z.object({\n    id,\n    type: RatingTypeSchema,\n});\n\nexport type UpdateCommentRatingOutput = Infer<typeof UpdateCommentRatingOutputSchema>;\nexport const UpdateCommentRatingOutputSchema = RatingSchema;\n\nexport type AnonimifyCommentInput = Infer<typeof AnonimifyCommentInputSchema>;\nexport const AnonimifyCommentInputSchema = z.object({\n    id,\n    reason: z.string().nonempty().nullable(),\n});\n\n// lenses\n\nexport const lensName = z.string().nonempty();\nexport const lensCode = z.string().nonempty();\n\nexport type GetLensesOutput = Infer<typeof GetLensesOutputSchema>;\nexport const GetLensesOutputSchema = z.array(\n    z.object({\n        id,\n        name: lensName,\n        code: lensCode,\n    }),\n);\n\nexport type CreateLensInput = Infer<typeof CreateLensInputSchema>;\nexport const CreateLensInputSchema = z.object({\n    name: lensName,\n    code: lensCode,\n});\n\nexport type UpdateLensInput = Infer<typeof UpdateLensInputSchema>;\nexport const UpdateLensInputSchema = z\n    .object({\n        id,\n\n        name: lensName.optional(),\n        code: lensCode.optional(),\n    });\n\n// hubs\n\nexport type GetHubsInput = Infer<typeof GetHubsInputSchema>;\nexport const GetHubsInputSchema = z\n    .object({\n        pagination: PaginationSchema,\n\n        ids: searchIds,\n        query: searchQuery,\n    })\n    .partial();\n\nexport type GetHubsOutput = Infer<typeof GetHubsOutputSchema>;\nexport const GetHubsOutputSchema = z.array(\n    z.object({\n        id,\n\n        headUser: SimpleUserSchema,\n\n        image: hubImage,\n\n        name: hubName,\n        description: hubDescription,\n\n        createdAt,\n        updatedAt,\n        deletedAt: deletedAt.optional(),\n    }),\n);\n\nexport type CreateHubInput = Infer<typeof CreateHubInputSchema>;\nexport const CreateHubInputSchema = z.object({\n    headUserId: id.nullable(),\n\n    name: hubName,\n    description: hubDescription,\n});\n\nexport type UpdateHubInput = Infer<typeof UpdateHubInputSchema>;\nexport const UpdateHubInputSchema = z\n    .object({\n        id,\n\n        name: hubName.optional(),\n        description: hubDescription.optional(),\n    });\n\n// communities\n\nexport type GetCommunitiesInput = Infer<typeof GetCommunitiesInputSchema>;\nexport const GetCommunitiesInputSchema = z\n    .object({\n        pagination: PaginationSchema,\n\n        ids: searchIds,\n        query: searchQuery,\n\n        hubId: id,\n    })\n    .partial();\n\nexport type GetCommunitiesOutput = Infer<typeof GetCommunitiesOutputSchema>;\nexport const GetCommunitiesOutputSchema = z.array(\n    z.object({\n        id,\n\n        hub: z\n            .object({\n                id,\n                name: hubName,\n                image: hubImage,\n            })\n            .nullable(),\n\n        headUser: SimpleUserSchema,\n\n        image: communityImage,\n\n        name: communityName,\n        description: communityDescription,\n\n        createdAt,\n        updatedAt,\n        deletedAt: deletedAt.optional(),\n    }),\n);\n\nexport type CreateCommunityInput = Infer<typeof CreateCommunityInputSchema>;\nexport const CreateCommunityInputSchema = z.object({\n    hubId: id.nullable(),\n    headUserId: id.nullable(),\n\n    name: communityName,\n    description: communityDescription,\n});\n\nexport type UpdateCommunityInput = Infer<typeof UpdateCommunityInputSchema>;\nexport const UpdateCommunityInputSchema = z\n    .object({\n        id,\n\n        name: communityName.optional(),\n        description: communityDescription.optional(),\n    });\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport {\n    id,\n    searchIds,\n    searchQuery,\n    deletedAt,\n    LocalizationsSchema,\n    PaginationSchema,\n} from \"./common\";\n\nexport const tagName = LocalizationsSchema.min(1);\n\nexport type GetTagsInput = Infer<typeof GetTagsInputSchema>;\nexport const GetTagsInputSchema = z\n    .object({\n        pagination: PaginationSchema,\n        ids: searchIds,\n        query: searchQuery,\n    })\n    .partial();\n\nexport type GetTagsOutput = Infer<typeof GetTagsOutputSchema>;\nexport const GetTagsOutputSchema = z.array(\n    z.object({\n        id,\n        name: tagName,\n        deletedAt: deletedAt.optional(),\n    }),\n);\n\nexport type CreateTagInput = Infer<typeof CreateTagInputSchema>;\nexport const CreateTagInputSchema = z.object({\n    name: tagName,\n});\n\nexport type UpdateTagInput = Infer<typeof UpdateTagInputSchema>;\nexport const UpdateTagInputSchema = z.object({\n    id,\n    name: tagName,\n});\n", "import type { Infer } from \"./types\";\n\nimport { z } from \"zod\";\nimport { id, LocalizationsSchema, PaginationSchema } from \"./common\";\nimport { SimpleUserSchema } from \"./user\";\n\n// karma\n\nexport const karmaPointQuantity = z.number().int();\nexport const karmaPointComment = LocalizationsSchema.min(1);\n\nexport type GetKarmaPointsInput = Infer<typeof GetKarmaPointsInputSchema>;\nexport const GetKarmaPointsInputSchema = z.object({\n    pagination: PaginationSchema,\n\n    userId: id,\n});\n\nexport type GetKarmaPointsOutput = Infer<typeof GetKarmaPointsOutputSchema>;\nexport const GetKarmaPointsOutputSchema = z.array(\n    z.object({\n        id,\n        author: SimpleUserSchema,\n        quantity: karmaPointQuantity,\n        comment: karmaPointComment,\n    }),\n);\n\nexport type SpendKarmaPointInput = Infer<typeof SpendKarmaPointInputSchema>;\nexport const SpendKarmaPointInputSchema = z.object({\n    sourceUserId: id,\n    targetUserId: id,\n    quantity: karmaPointQuantity,\n    comment: karmaPointComment,\n});\n\n// feedback\n\nexport const userFeedbackValue = z.number().int().min(0).max(10);\nexport const userFeedbackText = LocalizationsSchema.min(1);\n\nexport type GetUserFeedbacksInput = Infer<typeof GetUserFeedbacksInputSchema>;\nexport const GetUserFeedbacksInputSchema = z.object({\n    pagination: PaginationSchema,\n\n    userId: id,\n});\n\nexport type GetUserFeedbacksOutput = Infer<typeof GetUserFeedbacksOutputSchema>;\nexport const GetUserFeedbacksOutputSchema = z.array(\n    z.object({\n        id,\n        author: SimpleUserSchema.nullable(),\n        isAnonymous: z.boolean(),\n        value: userFeedbackValue,\n        text: userFeedbackText,\n    }),\n);\n\nexport type CreateUserFeedbackInput = Infer<typeof CreateUserFeedbackInputSchema>;\nexport const CreateUserFeedbackInputSchema = z.object({\n    sourceUserId: id,\n    targetUserId: id,\n    value: userFeedbackValue,\n    isAnonymous: z.boolean(),\n    text: userFeedbackText,\n});\n\n// summary\n\nexport type GetUserSummaryInput = Infer<typeof GetUserSummaryInputSchema>;\nexport const GetUserSummaryInputSchema = z.object({\n    userId: id,\n});\n\nexport type GetUserSummaryOutput = Infer<typeof GetUserSummaryOutputSchema>;\nexport const GetUserSummaryOutputSchema = z.object({\n    rating: z.number().int(),\n    karma: z.number().int(),\n    rate: z.number().min(0).max(10).nullable(),\n});\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAA,UAAS;;;ACFlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,YAAY;AAElB,IAAM,2BAA2B,CAAC,cAAc,aAAa,YAAY;AACzE,IAAM,sBAAsB,IAAI,OAAO;;;ADA9C,SAAS,SAAS;AAEX,IAAM,KAAK,EAAE,OAAO,EAAE,OAAO;AAC7B,IAAM,WAAW,GAAG,SAAS,EAAE,QAAQ,IAAI;AAE3C,IAAM,MAAM,EAAE,OAAO,EAAE,IAAI;AAC3B,IAAM,QAAQ,EAAE,OAAO,EAAE,MAAM;AAC/B,IAAM,QAAQ,EAAE,OAAO,EAAE,SAAS;AAElC,IAAM,WAAW,EAAE,OAAO,EAAE,SAAS;AACrC,IAAM,gBAAgB,SAAS,SAAS;AAExC,IAAM,YAAY,EAAE,KAAK;AACzB,IAAM,YAAY,EAAE,KAAK;AACzB,IAAM,YAAY,EAAE,KAAK,EAAE,SAAS;AAEpC,IAAM,YAAY,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AACnC,IAAM,cAAc,EAAE,OAAO,EAAE,SAAS;AAExC,IAAM,eAAe,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,KAAK,CAAC;AAErF,SAAS,mBAA4C,QAAW;AACnE,SAAO,EACF,OAAO,EACP,UAAU,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,EACtC,KAAK,EAAE,OAAO,MAAM,CAAC;AAC9B;AAEO,SAAS,iBAA0C,QAAW;AACjE,SAAO,EAAE,OAAO;AAAA,IACZ,MAAM,mBAAmB,MAAM;AAAA,EACnC,CAAC;AACL;AAGO,IAAM,qBAAqB,EAAE,OAAO,EAAE,GAAG,CAAC;AAG1C,IAAM,sBAAsB,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;AAG/C,IAAM,2BAA2B,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;AAGpD,IAAM,4BAA4B,EAAE,MAAM,wBAAwB,EAAE,IAAI,CAAC;AAGzE,IAAM,qBAAqB,EAAE,OAAO;AAAA,EACvC,QAAQ;AAAA,EACR,OAAO,EAAE,OAAO,EAAE,SAAS;AAC/B,CAAC;AAGM,IAAM,sBAAsB,EAAE,MAAM,kBAAkB;AAGtD,IAAM,cAAc,EAAE,OAAO;AAAA,EAChC;AAAA,EACA,KAAK,EAAE,OAAO;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AACf,CAAC;AAGM,IAAM,eAAe,EAAE,MAAM,WAAW;AAExC,IAAM,aAAa;AAAA,EACtB,QAAQ,EAAE,OAAO,OAAO,EACnB,IAAI,EACJ,QAAQ,CAAC;AAAA,EACd,OAAO,EAAE,OAAO,OAAO,EAClB,IAAI,EACJ,SAAS,EACT,IAAI,GAAG,EACP,QAAQ,SAAS;AAAA,EAEtB,MAAM,EAAE,OAAO,OAAO,EACjB,IAAI,EACJ,SAAS,EACT,QAAQ,CAAC;AAAA,EACd,MAAM,EAAE,OAAO,OAAO,EACjB,IAAI,EACJ,SAAS,EACT,IAAI,GAAG,EACP,QAAQ,SAAS;AAC1B;AAGO,IAAM,mBAAmB,EAC3B,OAAO;AAAA,EACJ,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AACrB,CAAC,EACA,QAAQ;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,SAAS,WACZ,QACA,OACW;AACX,SAAO,OAAO,MAAM,KAAK;AAC7B;AAEO,SAAS,aACZ,QACA,OACW;AACX,SAAO,OAAO,MAAM,KAAK;AAC7B;;;AD3GO,IAAM,uCAAuCC,GAAE,OAAO;AAAA,EACzD,YAAYA,GAAE,MAAM,EAAE;AAAA,EACtB,gBAAgBA,GAAE,MAAM,EAAE;AAAA,EAC1B,eAAeA,GAAE,MAAM,EAAE;AAAA,EACzB,qBAAqBA,GAAE,MAAM,EAAE;AACnC,CAAC;;;AGXD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;;;ACFlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;AAeX,IAAM,WAAW,oBAAoB,IAAI,CAAC;AAC1C,IAAM,kBAAkB;AACxB,IAAM,YAAY,SAAS,SAAS;AAEpC,IAAM,gBAAgB,oBAAoB,IAAI,CAAC;AAC/C,IAAM,oBAAoBC,GAAE,QAAQ;AACpC,IAAM,iBAAiBA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAEtD,IAAM,eAAeA,GAAE,OAAO,EAAE,SAAS;AAGzC,IAAM,iBAAiBA,GAAE,KAAK;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,gCAAgCA,GAAE,KAAK;AAAA,EAChD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAEM,IAAM,0BAA0B,8BAA8B,SAAS;AAGvE,IAAM,mBAAmBA,GAAE,OAAO;AAAA,EACrC;AAAA,EACA,MAAM;AAAA,EACN,OAAO;AACX,CAAC;AAGM,IAAM,oBAAoBA,GAAE,OAAO;AAAA,EACtC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EAEN,MAAM;AAAA,EACN,aAAa;AAAA,EAEb,OAAO,SAAS,SAAS;AAAA,EAEzB,qBAAqB;AAAA,EAErB;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,sBAAsBA,GAC9B,OAAO;AAAA,EACJ,YAAY;AAAA,EAEZ,KAAK;AAAA,EACL,OAAO;AACX,CAAC,EACA,QAAQ;AAGN,IAAM,sBAAsBA,GAAE,OAAO;AAAA,EACxC;AAAA,EACA,MAAM;AAAA,EAEN,MAAM;AAAA,EACN,aAAa;AAAA,EAEb,OAAO;AAAA,EAEP,qBAAqB;AAAA,EAErB;AAAA,EACA;AAAA,EACA,WAAW,UAAU,SAAS;AAClC,CAAC;AAGM,IAAM,uBAAuBA,GAAE,MAAM,mBAAmB;AAGxD,IAAM,wBAAwBA,GAChC,OAAO;AAAA,EACJ;AAAA,EAEA,MAAM,SAAS,SAAS;AAAA,EACxB,aAAa,gBAAgB,SAAS;AAAA,EAEtC,qBAAqB,wBAAwB,SAAS;AAC1D,CAAC;AAIE,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,QAAQ;AAAA,EAER,MAAM;AAAA,EAEN,UAAU;AAAA,EACV,OAAO;AACX,CAAC;AAGM,IAAM,6BAA6BA,GACrC,OAAO;AAAA,EACJ;AAAA,EAEA,MAAM,cAAc,SAAS;AAAA,EAE7B,UAAU,kBAAkB,SAAS;AAAA,EACrC,OAAO,eAAe,SAAS;AACnC,CAAC;AAGE,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C,QAAQ;AAAA,EACR,KAAK,UAAU,SAAS;AAAA,EACxB,UAAU,kBAAkB,SAAS;AACzC,CAAC;AAGM,IAAM,4BAA4BA,GAAE;AAAA,EACvCA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,QAAQ;AAAA,IAER,MAAM;AAAA,IAEN,UAAU;AAAA,IACV,OAAO;AAAA,IAEP;AAAA,IACA;AAAA,IACA,WAAW,UAAU,SAAS;AAAA,EAClC,CAAC;AACL;AAGO,IAAM,yBAAyBA,GAAE,OAAO;AAAA,EAC3C,QAAQ;AACZ,CAAC;AAGM,IAAM,0BAA0BA,GAAE,OAAO;AAAA,EAC5C,MAAM,aAAa,SAAS;AAChC,CAAC;AAGM,IAAM,4BAA4BA,GAAE,OAAO;AAAA,EAC9C,QAAQ;AAAA,EAER,MAAM,aAAa,SAAS;AAChC,CAAC;AAGM,IAAM,4BAA4BA,GAAE,OAAO;AAAA,EAC9C,YAAY;AAChB,CAAC;AAGM,IAAM,6BAA6BA,GAAE,MAAMA,GAAE,OAAO;AAAA,EACvD;AAAA,EACA;AAAA,EACA,MAAMA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EACrC,QAAQ;AAAA,EACR,QAAQA,GAAE,QAAQ;AACtB,CAAC,CAAC;AAGK,IAAM,8BAA8BA,GAAE,OAAO;AAAA,EAChD;AAAA,EACA,MAAMA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EACrC,QAAQ;AACZ,CAAC;AAGM,IAAM,8BAA8BA,GAAE,OAAO;AAAA,EAChD;AACJ,CAAC;;;ADpMM,IAAM,MAAMC,GAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;AAG1C,IAAM,qBAAqBA,GAAE,OAAO;AAAA,EACvC;AACJ,CAAC;AAGM,IAAM,sBAAsBA,GAAE,OAAO;AAAA,EACxC,QAAQA,GAAE,QAAQ;AACtB,CAAC;AAGM,IAAM,oBAAoBA,GAAE,OAAO;AAAA,EACtC,YAAY,GAAG,SAAS;AAAA,EACxB;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,oBAAoBA,GAAE,OAAO;AAAA,EACtC;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,yBAAyBA,GAAE,OAAO;AAAA,EAC3C;AAAA,EACA;AAAA,EACA,MAAM;AACV,CAAC;;;AEpCD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;AAkBX,IAAM,0BAA0BC,GAAE,KAAK,CAAC,MAAM,CAAC;AAE/C,IAAM,cAAc,oBAAoB,IAAI,CAAC;AAC7C,IAAM,qBAAqB;AAE3B,IAAM,yBAAyB;AAC/B,IAAM,oBAAoBA,GAAE,MAAM,CAAC,UAAU,WAAW,CAAC;AAGzD,IAAM,gCAAgCA,GAAE,OAAO;AAAA,EAClD,WAAW;AAAA,EACX,eAAe;AACnB,CAAC;AAGM,IAAM,yBAAyBA,GACjC,OAAO;AAAA,EACJ,YAAY;AAAA,EAEZ,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AACZ,CAAC,EACA,QAAQ;AAGN,IAAM,yBAAyBA,GAAE,OAAO;AAAA,EAC3C;AAAA,EAEA,MAAM;AAAA,EACN,aAAa;AAAA,EAEb,YAAYA,GAAE,OAAO;AAAA,IACjB,WAAW;AAAA,IACX,SAAS;AAAA,IAET,MAAM;AAAA,IAEN,OAAO;AAAA,EACX,CAAC;AAAA,EAED,aAAaA,GAAE,OAAO,EAAE,IAAI,EAAE,SAAS;AAAA,EAEvC,OAAO;AAAA,EAEP;AAAA,EACA;AAAA,EACA,WAAW,UAAU,SAAS;AAClC,CAAC;AAGM,IAAM,0BAA0BA,GAAE,MAAM,sBAAsB;AAG9D,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C,YAAY,GAAG,SAAS;AAAA,EAExB,MAAM;AAAA,EACN,aAAa;AACjB,CAAC;AAGM,IAAM,2BAA2BA,GACnC,OAAO;AAAA,EACJ;AAAA,EAEA,MAAM,YAAY,SAAS;AAAA,EAC3B,aAAa,mBAAmB,SAAS;AAC7C,CAAC;AAKE,IAAM,+BAA+BA,GAAE,OAAO;AAAA,EACjD,YAAY;AAAA,EAEZ,WAAW;AACf,CAAC;AAGM,IAAM,+BAA+BA,GAAE,OAAO;AAAA,EACjD;AAAA,EAEA,WAAW;AAAA,EACX,SAAS;AAAA,EAET,MAAM;AAAA,EAEN,OAAO;AAAA,EAEP;AAAA,EACA,WAAW,UAAU,SAAS;AAClC,CAAC;AAGM,IAAM,gCAAgCA,GAAE,MAAM,4BAA4B;AAG1E,IAAM,iCAAiCA,GAAE,OAAO;AAAA,EACnD,WAAW;AAAA,EACX,QAAQ;AACZ,CAAC;AAKM,IAAM,gCAAgCA,GAAE,KAAK,CAAC,WAAW,YAAY,YAAY,SAAS,CAAC;AAG3F,IAAM,mCAAmCA,GAAE,OAAO;AAAA,EACrD,YAAY;AAAA,EAEZ,WAAW,GAAG,SAAS;AAC3B,CAAC;AAGM,IAAM,oCAAoCA,GAAE;AAAA,EAC/CA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,WAAW;AAAA,IACX,QAAQ;AAAA,IAER,QAAQ;AAAA,IAER,WAAWA,GAAE,KAAK;AAAA,IAClB,WAAWA,GAAE,KAAK;AAAA,EACtB,CAAC;AACL;AAGO,IAAM,qCAAqCA,GAAE,OAAO;AAAA,EACvD,WAAW;AAAA,EACX,QAAQ;AACZ,CAAC;AAKM,IAAM,iCAAiCA,GAAE,KAAK,CAAC,WAAW,YAAY,UAAU,CAAC;AAGjF,IAAM,oCAAoCA,GAAE,OAAO;AAAA,EACtD,YAAY;AAAA,EAEZ,WAAW,GAAG,SAAS;AAC3B,CAAC;AAGM,IAAM,qCAAqCA,GAAE;AAAA,EAChDA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,WAAW;AAAA,IACX,QAAQ;AAAA,IAER,QAAQ;AAAA,IAER,WAAWA,GAAE,KAAK;AAAA,IAClB,WAAWA,GAAE,KAAK;AAAA,EACtB,CAAC;AACL;AAGO,IAAM,sCAAsCA,GAAE,OAAO;AAAA,EACxD,WAAW;AAAA,EACX,QAAQ;AACZ,CAAC;;;AC3LD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;;;ACFlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;AAUX,IAAM,UAAU,oBAAoB,IAAI,CAAC;AAGzC,IAAM,qBAAqBC,GAC7B,OAAO;AAAA,EACJ,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,OAAO;AACX,CAAC,EACA,QAAQ;AAGN,IAAM,sBAAsBA,GAAE;AAAA,EACjCA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,MAAM;AAAA,IACN,WAAW,UAAU,SAAS;AAAA,EAClC,CAAC;AACL;AAGO,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC,MAAM;AACV,CAAC;AAGM,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC;AAAA,EACA,MAAM;AACV,CAAC;;;ADpBM,IAAM,mBAAmBC,GAAE,KAAK,CAAC,QAAQ,SAAS,CAAC;AAGnD,IAAM,eAAeA,GAAE,OAAO;AAAA,EACjC,OAAOA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,EACpC,UAAUA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,EACvC,QAAQ,iBAAiB,SAAS;AACtC,CAAC;AAEM,IAAM,UAAU,oBAAoB,IAAI,CAAC;AACzC,IAAM,iBAAiB,oBAAoB,IAAI,CAAC;AAChD,IAAM,WAAW;AAEjB,IAAM,gBAAgB,oBAAoB,IAAI,CAAC;AAC/C,IAAM,uBAAuB,oBAAoB,IAAI,CAAC;AACtD,IAAM,iBAAiB;AAIvB,IAAM,iBAAiBA,GAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AAGrD,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC,OAAO,eAAe,SAAS;AAAA,EAC/B,OAAOA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,EACpC,YAAYA,GAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;AACnD,CAAC;AAEM,IAAM,YAAY,oBAAoB,IAAI,CAAC;AAC3C,IAAM,WAAW,oBAAoB,IAAI,CAAC;AAG1C,IAAM,kBAAkBA,GAAE,OAAO;AAAA,EACpC;AAAA,EACA,KAAK;AACT,CAAC;AAGM,IAAM,sBAAsBA,GAAE,OAAO;AAAA,EACxC;AAAA,EAEA,KAAKA,GACA,OAAO;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,IACN,OAAO;AAAA,EACX,CAAC,EACA,SAAS;AAAA,EAEd,WAAWA,GACN,OAAO;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,IACN,OAAO;AAAA,EACX,CAAC,EACA,SAAS;AAAA,EAEd,QAAQ;AAAA,EAER,QAAQ;AAAA,EACR,YAAY;AAAA,EAEZ,OAAO;AAAA,EACP,MAAM;AAAA,EAEN,MAAMA,GAAE;AAAA,IACJA,GAAE,OAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AAAA,EAEA;AAAA,EACA;AAAA,EACA,WAAW,UAAU,SAAS;AAClC,CAAC;AAGM,IAAM,sBAAsBA,GAAE,OAAO;AAAA,EACxC,YAAY;AAAA,EAEZ,IAAI,GAAG,SAAS;AAAA,EAChB,QAAQ,GAAG,SAAS;AACxB,CAAC;AAGM,IAAM,uBAAuBA,GAAE,MAAM,mBAAmB;AAGxD,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C;AACJ,CAAC;AAGM,IAAM,4BAA4BA,GAAE,MAAM,eAAe;AAGzD,IAAM,wBAAwBA,GAAE,OAAO;AAAA,EAC1C,OAAO,GAAG,SAAS;AAAA,EACnB,aAAa,GAAG,SAAS;AAAA,EACzB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQA,GAAE,MAAM,EAAE;AAAA,EAClB,UAAUA,GAAE,MAAM,EAAE;AACxB,CAAC;AAGM,IAAM,wBAAwBA,GAChC,OAAO;AAAA,EACJ;AAAA,EAEA,OAAO,UAAU,SAAS;AAAA,EAC1B,MAAM,SAAS,SAAS;AAAA,EACxB,QAAQA,GAAE,MAAM,EAAE,EAAE,SAAS;AAAA,EAC7B,UAAUA,GAAE,MAAM,EAAE,EAAE,SAAS;AACnC,CAAC;AAGE,IAAM,wBAAwBA,GAAE,OAAO;AAAA,EAC1C;AAAA,EAEA,QAAQA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC3C,CAAC;AAGM,IAAM,8BAA8BA,GAAE,OAAO;AAAA,EAChD;AAAA,EAEA,MAAM;AACV,CAAC;AAGM,IAAM,+BAA+B;AAGrC,IAAM,kCAAkCA,GAAE,OAAO;AAAA,EACpD;AAAA,EAEA,OAAO,eAAe,SAAS;AACnC,CAAC;AAGM,IAAM,mCAAmC;AAKzC,IAAM,0BAA0BA,GAAE,KAAK,CAAC,QAAQ,SAAS,CAAC;AAE1D,IAAM,cAAc,oBAAoB,IAAI,CAAC;AAG7C,IAAM,yBAAyBA,GAAE,MAAM;AAAA,EAC1CA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,YAAYA,GAAE,MAAM,EAAE,SAAS;AAAA,IAC/B,UAAUA,GAAE,MAAM,EAAE,SAAS;AAAA,EACjC,CAAC;AAAA,EACDA,GAAE,OAAO;AAAA,IACL,IAAIA,GAAE,MAAM,EAAE,SAAS;AAAA,IACvB,YAAY;AAAA,IACZ,UAAU;AAAA,EACd,CAAC;AACL,CAAC;AAGM,IAAM,0BAA0BA,GAAE;AAAA,EACrCA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,MAAMA,GAAE,OAAO,EAAE,SAAS;AAAA,IAE1B,QAAQ,iBAAiB,SAAS;AAAA,IAElC,aAAaA,GAAE,QAAQ;AAAA,IACvB,iBAAiBA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,IAEhD,QAAQ;AAAA,IAER,MAAM,YAAY,SAAS;AAAA,IAE3B,eAAeA,GAAE,OAAO,EAAE,IAAI,EAAE,YAAY;AAAA,IAE5C,cAAcA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,IAE7C;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAGO,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C,YAAY;AAAA,EACZ,UAAU;AAAA,EAEV,MAAM;AACV,CAAC;AAGM,IAAM,2BAA2BA,GACnC,OAAO;AAAA,EACJ;AAAA,EAEA,MAAM,YAAY,SAAS;AAC/B,CAAC;AAGE,IAAM,2BAA2BA,GAAE,OAAO;AAAA,EAC7C;AAAA,EAEA,QAAQA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC3C,CAAC;AAGM,IAAM,iCAAiCA,GAAE,OAAO;AAAA,EACnD;AAAA,EACA,MAAM;AACV,CAAC;AAGM,IAAM,kCAAkC;AAGxC,IAAM,8BAA8BA,GAAE,OAAO;AAAA,EAChD;AAAA,EACA,QAAQA,GAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC3C,CAAC;AAIM,IAAM,WAAWA,GAAE,OAAO,EAAE,SAAS;AACrC,IAAM,WAAWA,GAAE,OAAO,EAAE,SAAS;AAGrC,IAAM,wBAAwBA,GAAE;AAAA,EACnCA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACV,CAAC;AACL;AAGO,IAAM,wBAAwBA,GAAE,OAAO;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAGM,IAAM,wBAAwBA,GAChC,OAAO;AAAA,EACJ;AAAA,EAEA,MAAM,SAAS,SAAS;AAAA,EACxB,MAAM,SAAS,SAAS;AAC5B,CAAC;AAKE,IAAM,qBAAqBA,GAC7B,OAAO;AAAA,EACJ,YAAY;AAAA,EAEZ,KAAK;AAAA,EACL,OAAO;AACX,CAAC,EACA,QAAQ;AAGN,IAAM,sBAAsBA,GAAE;AAAA,EACjCA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,UAAU;AAAA,IAEV,OAAO;AAAA,IAEP,MAAM;AAAA,IACN,aAAa;AAAA,IAEb;AAAA,IACA;AAAA,IACA,WAAW,UAAU,SAAS;AAAA,EAClC,CAAC;AACL;AAGO,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACzC,YAAY,GAAG,SAAS;AAAA,EAExB,MAAM;AAAA,EACN,aAAa;AACjB,CAAC;AAGM,IAAM,uBAAuBA,GAC/B,OAAO;AAAA,EACJ;AAAA,EAEA,MAAM,QAAQ,SAAS;AAAA,EACvB,aAAa,eAAe,SAAS;AACzC,CAAC;AAKE,IAAM,4BAA4BA,GACpC,OAAO;AAAA,EACJ,YAAY;AAAA,EAEZ,KAAK;AAAA,EACL,OAAO;AAAA,EAEP,OAAO;AACX,CAAC,EACA,QAAQ;AAGN,IAAM,6BAA6BA,GAAE;AAAA,EACxCA,GAAE,OAAO;AAAA,IACL;AAAA,IAEA,KAAKA,GACA,OAAO;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,MACN,OAAO;AAAA,IACX,CAAC,EACA,SAAS;AAAA,IAEd,UAAU;AAAA,IAEV,OAAO;AAAA,IAEP,MAAM;AAAA,IACN,aAAa;AAAA,IAEb;AAAA,IACA;AAAA,IACA,WAAW,UAAU,SAAS;AAAA,EAClC,CAAC;AACL;AAGO,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,OAAO,GAAG,SAAS;AAAA,EACnB,YAAY,GAAG,SAAS;AAAA,EAExB,MAAM;AAAA,EACN,aAAa;AACjB,CAAC;AAGM,IAAM,6BAA6BA,GACrC,OAAO;AAAA,EACJ;AAAA,EAEA,MAAM,cAAc,SAAS;AAAA,EAC7B,aAAa,qBAAqB,SAAS;AAC/C,CAAC;;;AE9XL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,SAAS,KAAAC,UAAS;AAMX,IAAM,qBAAqBC,GAAE,OAAO,EAAE,IAAI;AAC1C,IAAM,oBAAoB,oBAAoB,IAAI,CAAC;AAGnD,IAAM,4BAA4BA,GAAE,OAAO;AAAA,EAC9C,YAAY;AAAA,EAEZ,QAAQ;AACZ,CAAC;AAGM,IAAM,6BAA6BA,GAAE;AAAA,EACxCA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,EACb,CAAC;AACL;AAGO,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,EACV,SAAS;AACb,CAAC;AAIM,IAAM,oBAAoBA,GAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AACxD,IAAM,mBAAmB,oBAAoB,IAAI,CAAC;AAGlD,IAAM,8BAA8BA,GAAE,OAAO;AAAA,EAChD,YAAY;AAAA,EAEZ,QAAQ;AACZ,CAAC;AAGM,IAAM,+BAA+BA,GAAE;AAAA,EAC1CA,GAAE,OAAO;AAAA,IACL;AAAA,IACA,QAAQ,iBAAiB,SAAS;AAAA,IAClC,aAAaA,GAAE,QAAQ;AAAA,IACvB,OAAO;AAAA,IACP,MAAM;AAAA,EACV,CAAC;AACL;AAGO,IAAM,gCAAgCA,GAAE,OAAO;AAAA,EAClD,cAAc;AAAA,EACd,cAAc;AAAA,EACd,OAAO;AAAA,EACP,aAAaA,GAAE,QAAQ;AAAA,EACvB,MAAM;AACV,CAAC;AAKM,IAAM,4BAA4BA,GAAE,OAAO;AAAA,EAC9C,QAAQ;AACZ,CAAC;AAGM,IAAM,6BAA6BA,GAAE,OAAO;AAAA,EAC/C,QAAQA,GAAE,OAAO,EAAE,IAAI;AAAA,EACvB,OAAOA,GAAE,OAAO,EAAE,IAAI;AAAA,EACtB,MAAMA,GAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;AAC7C,CAAC;", "names": ["z", "z", "z", "z", "z", "z", "z", "z", "z", "z", "z", "z", "z", "z"]}