{"version": 3, "sources": ["../src/acrpc/core.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport superjson from \"superjson\";\nimport {\n    CaseTransformer,\n    KebabCaseStrategy,\n    UnknownCaseStrategy,\n} from \"@ocelotjungle/case-converters\";\nimport objectInspect from \"object-inspect\";\n\nexport function log(...args: any[]) {\n    // console.log(...args);\n}\n\nexport function dir(...args: any[]) {\n    // console.log(...args.map(arg => objectInspect(arg, { depth: Number.MAX_SAFE_INTEGER })));\n}\n\nexport const kebabTransformer = new CaseTransformer(\n    new UnknownCaseStrategy(),\n    new KebabCaseStrategy(),\n);\n\nexport const methods = [\n    \"get\",\n    \"post\",\n    \"put\",\n    \"patch\",\n    \"delete\",\n] as const;\n\nexport type Method = typeof methods[number];\n\n/**\n * input = schema => input is validated against the schema\n * \n * input = null => input is nothing implicitly\n * \n * input = undefined => validation is disabled, input can be anything\n * \n * output = schema => output is validated against the schema\n * \n * output = null | undefined => validation is disabled, output can be anything\n */\nexport type SchemaEndpoint = {\n    input: z.ZodType | null | undefined;\n    output: z.ZodType | null | undefined;\n    /** @default true */\n    isMetadataUsed?: boolean;\n    /** @default true */\n    isMetadataRequired?: boolean;\n    cacheControl?: string;\n    /** @default 0 */\n    autoScopeInvalidationDepth?: number;\n    invalidate?: string[];\n};\n\nexport type SchemaRoute = Partial<Record<Method, SchemaEndpoint>>;\n\nexport interface Schema extends Record<string, Schema | SchemaRoute> { }\n\nexport type Transformer = {\n    serialize: (data: any) => string;\n    deserialize: (data: string) => any;\n}\n\nexport const jsonTransformer: Transformer = {\n    serialize: JSON.stringify,\n    deserialize: JSON.parse,\n}\n\nexport const superjsonTransformer: Transformer = {\n    serialize: superjson.stringify,\n    deserialize: superjson.parse,\n};\n"], "mappings": ";AAAA,OAAkB;AAClB,OAAO,eAAe;AACtB;AAAA,EACI;AAAA,EACA;AAAA,EACA;AAAA,OACG;AACP,OAA0B;AAEnB,SAAS,OAAO,MAAa;AAEpC;AAEO,SAAS,OAAO,MAAa;AAEpC;AAEO,IAAM,mBAAmB,IAAI;AAAA,EAChC,IAAI,oBAAoB;AAAA,EACxB,IAAI,kBAAkB;AAC1B;AAEO,IAAM,UAAU;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAqCO,IAAM,kBAA+B;AAAA,EACxC,WAAW,KAAK;AAAA,EAChB,aAAa,KAAK;AACtB;AAEO,IAAM,uBAAoC;AAAA,EAC7C,WAAW,UAAU;AAAA,EACrB,aAAa,UAAU;AAC3B;", "names": []}