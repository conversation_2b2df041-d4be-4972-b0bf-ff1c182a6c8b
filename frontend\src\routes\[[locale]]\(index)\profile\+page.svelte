<script lang="ts">
  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import { formatDate, removeCurrentUser } from "$lib";
  import EditProfileModal from "./edit-profile-modal.svelte";
  import UploadImageModal from "./upload-image-modal.svelte";
  import AlignmentSystemModal from "./alignment-system-modal.svelte";

  const i18n = {
    en: {
      _page: {
        title: "Profile — Commune",
      },
      loading: "Loading...",
      loadingProfile: "Loading your profile...",
      uploadImage: "Upload profile image",
      admin: "Administrator",
      user: "User",
      editProfile: "Edit Profile",
      signOut: "Sign Out",
      joined: "Joined",
      accountSummary: "Account Summary",
      accountType: {
        title: "Account Type",
        values: {
          admin: "Administrator",
          moderator: "Moderator",
          user: "User",
        },
      },
      daysAsMember: "Days as member",
      aboutMe: "About Me",
      noDescription: "No description available yet. Add one to tell others about yourself.",
      addDescription: "Add Description",
      alignmentSystem: "Alignment System",
      alignmentTypes: {
        lawfulGood: "Lawful Good",
        neutralGood: "Neutral Good",
        chaoticGood: "Chaotic Good",
        lawfulNeutral: "Lawful Neutral",
        trueNeutral: "True Neutral",
        chaoticNeutral: "Chaotic Neutral",
        lawfulEvil: "Lawful Evil",
        neutralEvil: "Neutral Evil",
        chaoticEvil: "Chaotic Evil",
      },
      noAlignment: "No alignment set",
      clickToSetAlignment: "Click to set your alignment",

      dateFormatLocale: "en-US",
    },

    ru: {
      _page: {
        title: "Профиль — Коммуна",
      },
      loading: "Загрузка...",
      loadingProfile: "Загрузка вашего профиля...",
      uploadImage: "Загрузить изображение профиля",
      admin: "Администратор",
      user: "Пользователь",
      editProfile: "Редактировать профиль",
      signOut: "Выйти",
      joined: "Присоединился",
      accountSummary: "Информация об аккаунте",
      accountType: {
        title: "Тип аккаунта",
        values: {
          admin: "Администратор",
          moderator: "Модератор",
          user: "Пользователь",
        },
      },
      daysAsMember: "Дней в качестве участника",
      aboutMe: "Обо мне",
      noDescription: "Нет описания. Добавьте описание, чтобы рассказать другим о себе.",
      addDescription: "Добавить описание",
      alignmentSystem: "Система мировоззрения",
      alignmentTypes: {
        lawfulGood: "Законопослушный добрый",
        neutralGood: "Нейтральный добрый",
        chaoticGood: "Хаотичный добрый",
        lawfulNeutral: "Законопослушный нейтральный",
        trueNeutral: "Истинно нейтральный",
        chaoticNeutral: "Хаотичный нейтральный",
        lawfulEvil: "Законопослушный злой",
        neutralEvil: "Нейтральный злой",
        chaoticEvil: "Хаотичный злой",
      },
      noAlignment: "Мировоззрение не установлено",
      clickToSetAlignment: "Нажмите, чтобы установить мировоззрение",

      dateFormatLocale: "ru-RU",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  let user = $state(data.me);
  let showEditModal = $state(false);
  let showUploadModal = $state(false);
  let showAlignmentModal = $state(false);

  const name = $derived(getAppropriateLocalization(user.name));
  const description = $derived(getAppropriateLocalization(user.description));

  const handleLogout = async () => {
    try {
      await api.auth.signOut.get();
    } catch (error) {
      console.error("Error during logout:", error);
    } finally {
      // Always clear local user data and redirect
      removeCurrentUser();
      goto("/");
    }
  };

  const joinDate = $derived(new Date(user.createdAt));

  const getAlignmentImagePath = (alignment: string) => {
    // Convert camelCase to kebab-case for file names
    const fileName = alignment.replace(/([A-Z])/g, "-$1").toLowerCase();
    return `/images/alignment-system/kung-fu-panda/${fileName}.png`;
  };

  function refresh() {
    window.location.reload();
  }
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container-fluid py-4">
  <div class="row g-4">
    <!-- Sidebar with user info -->
    <div class="col-lg-3">
      <div class="card border-0 shadow-sm h-100">
        <div class="text-center p-4">
          <div class="position-relative mx-auto mb-3" style:width="120px" style:height="120px">
            <!-- svelte-ignore a11y_click_events_have_key_events -->
            <div
              class="bg-light rounded-circle overflow-hidden border"
              style:width="100%"
              style:height="100%"
              role="img"
            >
              <!-- User avatar or fallback -->
              <div class="position-relative w-100 h-100">
                <img
                  src={user.image ? `/images/${user.image}` : "/images/default-avatar.png"}
                  alt={`${name}'s avatar`}
                  width={120}
                  height={120}
                  style:width="100%"
                  style:height="100%"
                  style:object-fit="cover"
                />
              </div>
            </div>
            <button
              class="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 border-0"
              style:width="30px"
              style:height="30px"
              style:display="flex"
              style:align-items="center"
              style:justify-content="center"
              onclick={() => (showUploadModal = true)}
              title={t.uploadImage}
              aria-label={t.uploadImage}
            >
              <i class="bi bi-plus"></i>
            </button>
          </div>

          <h4 class="fw-bold mb-1">{name}</h4>
          {#if user.role === "admin"}
            <span class={`badge bg-danger mb-3`}>
              {t.admin}
            </span>
          {/if}

          <div class="d-grid gap-2 mt-3">
            <button class="btn btn-primary" onclick={() => (showEditModal = true)}>
              <i class="bi bi-gear me-2"></i>
              {t.editProfile}
            </button>
            <button class="btn btn-outline-danger" onclick={handleLogout}>
              <i class="bi bi-box-arrow-right me-2"></i>
              {t.signOut}
            </button>
          </div>
        </div>

        <div class="card-footer bg-light border-top p-3">
          <div class="d-flex align-items-center mb-2">
            <i class="bi bi-envelope text-muted me-2"></i>
            <div class="text-truncate">
              <small class="text-muted">{user.email}</small>
            </div>
          </div>
          <div class="d-flex align-items-center">
            <i class="bi bi-calendar3 text-muted me-2"></i>
            <div>
              <small class="text-muted">{t.joined} {formatDate(joinDate, locale)}</small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <div class="col-lg-9">
      <!-- Account summary -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-transparent border-bottom-0 pb-0">
          <h5 class="fw-bold">{t.accountSummary}</h5>
        </div>
        <div class="card-body">
          <div class="row g-4">
            <!-- <div class="col-md-4">
              <div class="border rounded p-3 text-center h-100">
                <div class="mb-2">
                  <i class="bi bi-shield-check fs-3 text-primary"></i>
                </div>
                <h2 class="mb-0 fw-bold">{t.accountType.values[user.role]}</h2>
                <p class="text-muted mb-0">{t.accountType.title}</p>
              </div>
            </div> -->
            <div class="col-md-4">
              <div class="border rounded p-3 text-center h-100">
                <div class="mb-2">
                  <i class="bi bi-calendar-check fs-3 text-primary"></i>
                </div>
                <h2 class="mb-0 fw-bold">
                  {Math.floor((new Date().getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24))}
                </h2>
                <p class="text-muted mb-0">{t.daysAsMember}</p>
              </div>
            </div>
            <!-- <div class="col-md-4">
            <div class="border rounded p-3 text-center h-100">
              <div class="mb-2">
                <i class="bi bi-person-badge fs-3 text-primary"></i>
              </div>
              <h2 class="mb-0 fw-bold">{userData.id.slice(0, 6)}</h2>
              <p class="text-muted mb-0">User ID</p>
            </div>
          </div> -->
          </div>
        </div>
      </div>

      <!-- Alignment System section -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-transparent border-bottom-0 pb-0">
          <h5 class="fw-bold">{t.alignmentSystem}</h5>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="me-3">
              {#if user.alignmentSystemType}
                <button
                  class="btn btn-link p-0 border-0"
                  onclick={() => (showAlignmentModal = true)}
                  aria-label={t.clickToSetAlignment}
                >
                  <img
                    src={getAlignmentImagePath(user.alignmentSystemType)}
                    alt={t.alignmentTypes[user.alignmentSystemType]}
                    style="width: 64px; height: 64px; object-fit: contain;"
                    onerror={(e) => ((e.currentTarget as HTMLImageElement).style.display = "none")}
                  />
                </button>
              {:else}
                <button
                  class="btn btn-outline-secondary d-flex align-items-center justify-content-center"
                  style="width: 64px; height: 64px;"
                  onclick={() => (showAlignmentModal = true)}
                  aria-label={t.clickToSetAlignment}
                >
                  <span class="text-muted">—</span>
                </button>
              {/if}
            </div>
            <div>
              <h6 class="mb-1">
                {user.alignmentSystemType
                  ? t.alignmentTypes[user.alignmentSystemType]
                  : t.noAlignment}
              </h6>
              <small class="text-muted">{t.clickToSetAlignment}</small>
            </div>
          </div>
        </div>
      </div>

      <!-- About section -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-header d-flex justify-content-between align-items-center bg-transparent">
          <h5 class="fw-bold mb-0">{t.aboutMe}</h5>
        </div>
        <div class="card-body">
          {#if description}
            <p class="mb-0">{description}</p>
          {:else}
            <div class="text-center text-muted py-4">
              <i class="bi bi-file-earmark-text fs-1 mb-2"></i>
              <p>{t.noDescription}</p>
              <button
                onclick={() => (showEditModal = true)}
                class="btn btn-sm btn-primary"
                aria-label={t.addDescription}
              >
                <i class="bi bi-plus-circle me-1"></i>
                {t.addDescription}
              </button>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Profile Modal -->
  <EditProfileModal
    {locale}
    show={showEditModal}
    onHide={() => (showEditModal = false)}
    userData={user || null}
    onProfileUpdated={refresh}
  />

  <!-- Upload Image Modal -->
  <UploadImageModal
    {locale}
    show={showUploadModal}
    onHide={() => (showUploadModal = false)}
    userId={user.id}
    onImageUploaded={refresh}
  />

  <!-- Alignment System Modal -->
  <AlignmentSystemModal
    {locale}
    show={showAlignmentModal}
    onHide={() => (showAlignmentModal = false)}
    currentAlignment={user.alignmentSystemType}
    userId={user.id}
    onAlignmentUpdated={refresh}
  />
</div>
