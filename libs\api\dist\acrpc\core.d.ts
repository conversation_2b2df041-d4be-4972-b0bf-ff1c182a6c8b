import { z } from 'zod';
import { CaseTransformer } from '@ocelotjungle/case-converters';

declare function log(...args: any[]): void;
declare function dir(...args: any[]): void;
declare const kebabTransformer: CaseTransformer;
declare const methods: readonly ["get", "post", "put", "patch", "delete"];
type Method = typeof methods[number];
/**
 * input = schema => input is validated against the schema
 *
 * input = null => input is nothing implicitly
 *
 * input = undefined => validation is disabled, input can be anything
 *
 * output = schema => output is validated against the schema
 *
 * output = null | undefined => validation is disabled, output can be anything
 */
type SchemaEndpoint = {
    input: z.ZodType | null | undefined;
    output: z.ZodType | null | undefined;
    /** @default true */
    isMetadataUsed?: boolean;
    /** @default true */
    isMetadataRequired?: boolean;
    cacheControl?: string;
    /** @default 0 */
    autoScopeInvalidationDepth?: number;
    invalidate?: string[];
};
type SchemaRoute = Partial<Record<Method, SchemaEndpoint>>;
interface Schema extends Record<string, Schema | SchemaRoute> {
}
type Transformer = {
    serialize: (data: any) => string;
    deserialize: (data: string) => any;
};
declare const jsonTransformer: Transformer;
declare const superjsonTransformer: Transformer;

export { type Method, type Schema, type SchemaEndpoint, type SchemaRoute, type Transformer, dir, jsonTransformer, kebabTransformer, log, methods, superjsonTransformer };
